<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="1200px"
    :before-close="handleClose"
    :show-close="false"
    append-to-body>

    <div v-loading="loading" class="detail-content">
      <!-- 项目/任务名称标题 -->
      <div class="project-title">
        项目/任务名称：{{ detailData.projectTaskName || '-' }}
      </div>

      <!-- 表格内容区域 -->
      <div class="table-content">
        <el-table
          :data="tableData"
          border
          stripe
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          style="width: 100%; margin-bottom: 20px;">

          <el-table-column prop="label" label="字段名称" width="150" align="center"></el-table-column>
          <el-table-column prop="value" label="字段值" align="center">
            <template slot-scope="scope">
              <div v-html="scope.row.value"></div>
            </template>
          </el-table-column>
          <el-table-column prop="label2" label="字段名称" width="150" align="center"></el-table-column>
          <el-table-column prop="value2" label="字段值" align="center">
            <template slot-scope="scope">
              <div v-html="scope.row.value2"></div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 需求背景表格 -->
      <div class="requirement-table">
        <el-table
          :data="backgroundData"
          border
          stripe
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', textAlign: 'center' }"
          :cell-style="{ textAlign: 'left' }"
          style="width: 100%;">

          <el-table-column prop="label" label="需求背景" width="150" align="center"></el-table-column>
          <el-table-column prop="value" label="内容" align="left">
            <template slot-scope="scope">
              <div class="background-text-container">
                <div class="background-text">{{ scope.row.value }}</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { projectResultDetail } from "@/api/project/projectResult"
import { parseTime } from "@/utils/ruoyi"

export default {
  name: "DetailDialog",
  dicts: [
    'project_outcome_types',
    'project_outcome_status',
    'project_outcome_priority_level',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_project_manager',
    'project_outcome_dev_dept',
    'project_outcome_test_dept'
  ],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: '项目成果详情'
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      detailData: {}
    }
  },
  computed: {
    // 表格数据
    tableData() {
      return [
        {
          label: '业务类型',
          value: this.detailData.businessTypeName || '-',
          label2: '状态',
          value2: this.getDictLabel(this.detailData.status, this.dict.type.project_outcome_status) || '-'
        },
        {
          label: '优先级',
          value: this.detailData.priorityLevel || '-',
          label2: '完成时间',
          value2: this.formatTime(this.detailData.completionTime)
        },
        {
          label: '项目里程碑',
          value: this.formatMilestone(this.detailData),
          label2: '任务说明/进度',
          value2: this.formatProgress(this.detailData)
        },
        {
          label: '干系人',
          value: this.formatStakeholders(this.detailData),
          label2: '投入人力',
          value2: this.formatManpower(this.detailData)
        },
        {
          label: '工作量（人日）',
          value: this.formatWorkload(this.detailData),
          label2: '负责项目经理',
          value2: this.convertProjectManagerIdsToNames(this.detailData.projectManagers) || '-'
        },
        {
          label: '所属业务大类',
          value: this.getDictLabel(this.detailData.businessCategoryMajor, this.dict.type.project_outcome_business_category_major) || '-',
          label2: '所属业务小类',
          value2: this.getDictLabel(this.detailData.businessCategoryMinor, this.dict.type.project_outcome_business_category_minor) || '-'
        },
        {
          label: '成果类型',
          value: this.getDictLabel(this.detailData.resultType, this.dict.type.project_outcome_types) || '-',
          label2: '创建人',
          value2: this.detailData.createdBy || '-'
        },
        {
          label: '创建时间',
          value: this.formatTime(this.detailData.createdTime),
          label2: '',
          value2: ''
        }
      ]
    },
    // 需求背景数据
    backgroundData() {
      return [
        {
          label: '需求背景',
          value: this.detailData.requirementBackground || ''
        }
      ]
    }
  },
  watch: {
    dialogVisible(val) {
      if (val && this.recordId) {
        this.getDetailData()
      }
    },
    recordId(val) {
      if (val && this.dialogVisible) {
        this.getDetailData()
      }
    }
  },
  methods: {
    /** 获取详情数据 */
    getDetailData() {
      if (!this.recordId) return
      
      this.loading = true
      projectResultDetail(this.recordId).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.detailData = res.data || {}
        } else {
          this.$message.error(res.msg || '获取详情失败')
        }
      }).catch(() => {
        this.loading = false
        this.$message.error('获取详情失败')
      })
    },
    
    /** 关闭弹窗 */
    handleClose() {
      this.$emit('update:dialogVisible', false)
      this.detailData = {}
    },
    
    /** 获取状态类型 */
    getStatusType(status) {
      const typeMap = {
        '1': 'info',
        '2': 'warning', 
        '3': 'success',
        '4': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    /** 获取优先级类型 */
    getPriorityType(priority) {
      const typeMap = {
        'P1': 'danger',
        'P2': 'warning',
        'P3': 'info'
      }
      return typeMap[priority] || 'info'
    },
    
    /** 根据value获取字典label */
    getDictLabel(value, dictData) {
      if (!value || !dictData) return value
      const item = dictData.find(item => item.value === value)
      return item ? item.label : value
    },

    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '-'
      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
    },

    /** 格式化日期 */
    formatDate(date) {
      if (!date) return '-'
      return parseTime(date, '{y}-{m}-{d}')
    },



    /** 格式化项目里程碑 */
    formatMilestone(row) {
      const milestones = []
      if (row.milestoneRequirements) {
        milestones.push(`完成评审：${this.formatDate(row.milestoneRequirements)}`)
      }
      if (row.milestoneDevelopment) {
        milestones.push(`完成开发：${this.formatDate(row.milestoneDevelopment)}`)
      }
      if (row.milestoneTest) {
        milestones.push(`完成测试验收：${this.formatDate(row.milestoneTest)}`)
      }
      if (row.milestoneOnline) {
        milestones.push(`完成上线：${this.formatDate(row.milestoneOnline)}`)
      }
      return milestones.join('<br/>')
    },

    /** 格式化任务说明/进度 */
    formatProgress(row) {
      const progress = []
      if (row.requirementsProgress !== null && row.requirementsProgress !== undefined) {
        progress.push(`需求评审：${row.requirementsProgress}%`)
      }
      if (row.developmentProgress !== null && row.developmentProgress !== undefined) {
        progress.push(`开发进度：${row.developmentProgress}%`)
      }
      if (row.testProgress !== null && row.testProgress !== undefined) {
        progress.push(`测试验收进度：${row.testProgress}%`)
      }
      return progress.join('<br/>')
    },

    /** 格式化干系人 */
    formatStakeholders(row) {
      const stakeholders = []
      if (row.productManagers) {
        stakeholders.push(`产品：${row.productManagers}`)
      }
      if (row.devTeams) {
        const devTeamNames = this.convertDeptIdsToNames(row.devTeams, 'dev')
        stakeholders.push(`开发：${devTeamNames}`)
      }
      if (row.testTeams) {
        const testTeamNames = this.convertDeptIdsToNames(row.testTeams, 'test')
        stakeholders.push(`测试：${testTeamNames}`)
      }
      return stakeholders.join('<br/>')
    },

    /** 格式化投入人力 */
    formatManpower(row) {
      const manpower = []
      if (row.devManpower) {
        manpower.push(`开发：${row.devManpower}人`)
      }
      if (row.testManpower) {
        manpower.push(`测试：${row.testManpower}人`)
      }
      return manpower.join('<br/>')
    },

    /** 格式化工作量（人日） */
    formatWorkload(row) {
      const workload = []
      if (row.devWorkload) {
        workload.push(`开发：${row.devWorkload}人日`)
      }
      if (row.testWorkload) {
        workload.push(`测试：${row.testWorkload}人日`)
      }
      return workload.join('<br/>')
    },

    /** 将部门ID转换为部门名称 */
    convertDeptIdsToNames(deptIds, type) {
      if (!deptIds) return ''
      const ids = deptIds.split(',')
      const deptDict = type === 'dev' ? this.dict.type.project_outcome_dev_dept : this.dict.type.project_outcome_test_dept
      if (!deptDict) return deptIds

      const names = ids.map(id => {
        const dept = deptDict.find(item => item.value === id.trim())
        return dept ? dept.label : id
      })
      return names.join('、')
    },

    /** 将项目经理ID转换为项目经理名称 */
    convertProjectManagerIdsToNames(projectManagers) {
      if (!projectManagers) return ''
      const ids = projectManagers.split(',')
      const managerDict = this.dict.type.project_outcome_project_manager
      if (!managerDict) return projectManagers

      const names = ids.map(id => {
        const manager = managerDict.find(item => item.value === id.trim())
        return manager ? manager.label : id
      })
      return names.join('、')
    }
  }
}
</script>

<style scoped>
.detail-content {
  padding: 20px;
  background: #f8f9fa;
}

/* 项目标题样式 */
.project-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 30px;
  padding: 15px;
  border-radius: 4px;
  background: white;
  border: 1px solid #dcdfe6;
}

/* 表格内容区域 */
.table-content {
  margin-bottom: 20px;
}

/* 需求背景表格 */
.requirement-table {
  margin-top: 20px;
}

/* 需求背景容器样式 */
.background-text-container {
  position: relative;
  min-height: 120px;
  max-height: 400px;
  resize: vertical;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
}

/* 需求背景文本样式 */
.background-text {
  color: #606266;
  line-height: 1.6;
  padding: 10px;
  word-break: break-all;
  min-height: 100px;
  max-height: 380px;
  overflow-y: auto;
  white-space: pre-wrap;
}

/* 滚动条样式美化 */
.background-text::-webkit-scrollbar {
  width: 6px;
}

.background-text::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.background-text::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.background-text::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 对话框底部 */
.dialog-footer {
  text-align: center;
  padding: 20px;
}

/* 表格样式优化 */
::v-deep .el-table {
  border-radius: 4px;
  overflow: hidden;
}

::v-deep .el-table th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-weight: bold;
}

::v-deep .el-table td {
  padding: 12px 0;
}

::v-deep .el-table--border td {
  border-right: 1px solid #ebeef5;
}

::v-deep .el-table--border th {
  border-right: 1px solid #ebeef5;
}

/* 表格内容对齐 */
::v-deep .el-table .cell {
  padding: 0 10px;
  line-height: 1.6;
  word-break: break-all;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .detail-content {
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .detail-content {
    padding: 10px;
  }

  .project-title {
    font-size: 16px;
    padding: 10px;
  }
}
</style>
